/**
 * 腳本：統一API輪換修復驗證_debug.gs
 * 🔍 驗證統一API輪換系統修復狀態
 * 🎯 按照工作報表要求檢查Audio Dialog和TTS功能
 */

/**
 * 🧪 完整的API功能驗證測試
 */
function 統一API輪換修復驗證_debug() {
  console.log('🧪 ===== 統一API輪換修復驗證開始 =====');
  console.log(`⏰ 測試開始時間: ${new Date().toLocaleString('zh-TW')}`);
  
  try {
    const testResult = {
      timestamp: new Date().toISOString(),
      configTest: null,
      audioDialogTest: null,
      ttsTest: null,
      keyRotationTest: null,
      overallSuccess: false
    };
    
    // 測試1：配置檢查
    console.log('\n📝 測試1：配置檢查...');
    testResult.configTest = testConfiguration();
    
    // 測試2：Audio Dialog功能
    console.log('\n🎙️ 測試2：Audio Dialog功能...');
    testResult.audioDialogTest = testAudioDialog();
    
    // 測試3：TTS功能
    console.log('\n🔊 測試3：TTS功能...');
    testResult.ttsTest = testTTSFunction();
    
    // 測試4：KEY輪換機制
    console.log('\n🔄 測試4：KEY輪換機制...');
    testResult.keyRotationTest = testKeyRotation();
    
    // 評估整體結果
    const allTestsPassed = testResult.configTest?.passed && 
                          testResult.audioDialogTest?.passed && 
                          testResult.ttsTest?.passed &&
                          testResult.keyRotationTest?.passed;
    
    testResult.overallSuccess = allTestsPassed;
    
    // 輸出結果摘要
    console.log('\n📊 測試結果摘要:');
    console.log(`  配置檢查: ${testResult.configTest?.passed ? '✅' : '❌'}`);
    console.log(`  Audio Dialog: ${testResult.audioDialogTest?.passed ? '✅' : '❌'}`);
    console.log(`  TTS功能: ${testResult.ttsTest?.passed ? '✅' : '❌'}`);
    console.log(`  KEY輪換: ${testResult.keyRotationTest?.passed ? '✅' : '❌'}`);
    console.log(`  整體成功: ${allTestsPassed ? '✅' : '❌'}`);
    
    if (allTestsPassed) {
      console.log('\n🎉 所有測試通過！統一API輪換系統運作正常');
    } else {
      console.log('\n❌ 部分測試失敗，需要進一步修復');
      
      // 輸出具體的失敗信息
      if (!testResult.configTest?.passed) {
        console.log(`  配置問題: ${testResult.configTest?.error}`);
      }
      if (!testResult.audioDialogTest?.passed) {
        console.log(`  Audio Dialog問題: ${testResult.audioDialogTest?.error}`);
      }
      if (!testResult.ttsTest?.passed) {
        console.log(`  TTS問題: ${testResult.ttsTest?.error}`);
      }
      if (!testResult.keyRotationTest?.passed) {
        console.log(`  KEY輪換問題: ${testResult.keyRotationTest?.error}`);
      }
    }
    
    console.log(`\n⏰ 測試結束時間: ${new Date().toLocaleString('zh-TW')}`);
    console.log('🎉 ===== 統一API輪換修復驗證完成 =====');
    
    return testResult;
    
  } catch (error) {
    console.error('❌ 驗證過程發生錯誤:', error);
    return {
      timestamp: new Date().toISOString(),
      overallSuccess: false,
      error: error.message
    };
  }
}

/**
 * 📝 測試配置狀態
 */
function testConfiguration() {
  try {
    const config = getConfig();
    
    const result = {
      passed: false,
      apiKeyExists: !!config.geminiApiKey,
      ttsModel: config.ttsModel,
      audioDialogModel: config.audioDialogModel,
      keyCount: 0,
      currentIndex: 0
    };
    
    // 檢查API Key配置
    if (config.geminiApiKey) {
      const keyString = getConfigValue('geminiApiKey');
      const keys = parseMultipleApiKeys(keyString);
      result.keyCount = keys.length;
      result.currentIndex = getApiKeyRotationIndex();
      
      console.log(`  API Key數量: ${result.keyCount}`);
      console.log(`  當前索引: ${result.currentIndex}`);
      console.log(`  TTS模型: ${result.ttsModel}`);
      console.log(`  Audio Dialog模型: ${result.audioDialogModel}`);
      
      result.passed = result.keyCount > 0;
    }
    
    return result;
    
  } catch (error) {
    console.log(`  ❌ 配置檢查失敗: ${error.message}`);
    return {
      passed: false,
      error: error.message
    };
  }
}

/**
 * 🎙️ 測試Audio Dialog功能
 */
function testAudioDialog() {
  try {
    console.log('  測試Audio Dialog配置和調用...');
    
    // 檢查函數存在性
    if (typeof callGeminiAudioDialog !== 'function') {
      throw new Error('callGeminiAudioDialog函數不存在');
    }
    
    // 嘗試調用（使用短文本避免長時間等待）
    const testText = '測試';
    const startTime = Date.now();
    
    try {
      const result = callGeminiAudioDialog(testText, {});
      const endTime = Date.now();
      
      console.log(`  調用時間: ${endTime - startTime}ms`);
      console.log(`  調用結果: ${result.success ? '成功' : '失敗'}`);
      
      if (result.success) {
        console.log(`  音頻URL: ${result.audioUrl ? '已生成' : '未生成'}`);
        console.log(`  使用模型: ${result.modelUsed || 'unknown'}`);
      }
      
      return {
        passed: result.success,
        responseTime: endTime - startTime,
        hasAudioUrl: !!result.audioUrl,
        modelUsed: result.modelUsed,
        error: result.success ? null : result.error
      };
      
    } catch (apiError) {
      console.log(`  ❌ API調用失敗: ${apiError.message}`);
      
      // 檢查是否為預期的錯誤類型
      const errorMsg = apiError.message || '';
      const isConfigError = errorMsg.includes('404') || errorMsg.includes('not found');
      const isQuotaError = errorMsg.includes('429') || errorMsg.includes('quota');
      
      return {
        passed: false,
        error: apiError.message,
        errorType: isConfigError ? 'config' : isQuotaError ? 'quota' : 'unknown'
      };
    }
    
  } catch (error) {
    console.log(`  ❌ Audio Dialog測試失敗: ${error.message}`);
    return {
      passed: false,
      error: error.message
    };
  }
}

/**
 * 🔊 測試TTS功能
 */
function testTTSFunction() {
  try {
    console.log('  測試TTS配置和調用...');
    
    // 檢查函數存在性
    if (typeof textToSpeechWithGemini !== 'function') {
      throw new Error('textToSpeechWithGemini函數不存在');
    }
    
    // 嘗試調用（使用短文本）
    const testText = '測試';
    const startTime = Date.now();
    
    try {
      const result = textToSpeechWithGemini(testText, {});
      const endTime = Date.now();
      
      console.log(`  調用時間: ${endTime - startTime}ms`);
      console.log(`  調用結果: ${result.success ? '成功' : '失敗'}`);
      
      if (result.success) {
        console.log(`  音頻URL: ${result.audioUrl ? '已生成' : '未生成'}`);
        console.log(`  可播放: ${result.isPlayable ? '是' : '否'}`);
      }
      
      return {
        passed: result.success,
        responseTime: endTime - startTime,
        hasAudioUrl: !!result.audioUrl,
        isPlayable: result.isPlayable,
        error: result.success ? null : result.error
      };
      
    } catch (apiError) {
      console.log(`  ❌ TTS API調用失敗: ${apiError.message}`);
      
      // 檢查錯誤類型
      const errorMsg = apiError.message || '';
      const isContentError = errorMsg.includes('content') || errorMsg.includes('parts');
      const isQuotaError = errorMsg.includes('429') || errorMsg.includes('quota');
      
      return {
        passed: false,
        error: apiError.message,
        errorType: isContentError ? 'content' : isQuotaError ? 'quota' : 'unknown'
      };
    }
    
  } catch (error) {
    console.log(`  ❌ TTS測試失敗: ${error.message}`);
    return {
      passed: false,
      error: error.message
    };
  }
}

/**
 * 🔄 測試KEY輪換機制
 */
function testKeyRotation() {
  try {
    console.log('  測試KEY輪換機制...');
    
    const beforeIndex = getApiKeyRotationIndex();
    console.log(`  輪換前索引: ${beforeIndex}`);
    
    // 執行輪換
    rotateToNextApiKey();
    
    const afterIndex = getApiKeyRotationIndex();
    console.log(`  輪換後索引: ${afterIndex}`);
    
    const rotationSuccess = afterIndex === beforeIndex + 1;
    console.log(`  輪換成功: ${rotationSuccess ? '✅' : '❌'}`);
    
    return {
      passed: rotationSuccess,
      beforeIndex: beforeIndex,
      afterIndex: afterIndex
    };
    
  } catch (error) {
    console.log(`  ❌ KEY輪換測試失敗: ${error.message}`);
    return {
      passed: false,
      error: error.message
    };
  }
}

/**
 * 🚀 快速診斷_debug
 * 快速檢查系統狀態
 */
function 快速診斷_debug() {
  console.log('🚀 ===== 快速診斷開始 =====');
  
  try {
    // 基本配置檢查
    const config = getConfig();
    console.log(`API Key: ${config.geminiApiKey ? '✅ 已設定' : '❌ 未設定'}`);
    console.log(`TTS模型: ${config.ttsModel}`);
    console.log(`Audio Dialog模型: ${config.audioDialogModel}`);
    
    // 函數存在性檢查
    console.log(`callGeminiAudioDialog: ${typeof callGeminiAudioDialog === 'function' ? '✅' : '❌'}`);
    console.log(`textToSpeechWithGemini: ${typeof textToSpeechWithGemini === 'function' ? '✅' : '❌'}`);
    console.log(`callApiWithRetry: ${typeof callApiWithRetry === 'function' ? '✅' : '❌'}`);
    
    // KEY狀態檢查
    const keyString = getConfigValue('geminiApiKey');
    const keys = parseMultipleApiKeys(keyString);
    const currentIndex = getApiKeyRotationIndex();
    
    console.log(`API Key數量: ${keys.length}`);
    console.log(`當前索引: ${currentIndex}`);
    console.log(`當前Key: ${keys[currentIndex % keys.length] ? keys[currentIndex % keys.length].substring(0, 20) + '...' : 'N/A'}`);
    
    console.log('\n🎉 ===== 快速診斷完成 =====');
    
  } catch (error) {
    console.error('❌ 快速診斷失敗:', error);
  }
}
