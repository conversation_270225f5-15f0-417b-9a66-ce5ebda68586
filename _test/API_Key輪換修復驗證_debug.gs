/**
 * 腳本：API_Key輪換修復驗證_debug.gs
 * 🔍 驗證API Key輪換系統修復是否成功
 * 🔧 v2.0 - 增加實際 429 錯誤模擬和詳細狀態檢查
 */

/**
 * 🧪 API Key輪換修復驗證_debug
 * 驗證修復後的API Key輪換系統
 */
function API_Key輪換修復驗證_debug() {
  console.log('🧪 ===== API Key輪換修復驗證開始 =====');
  console.log(`⏰ 測試開始時間: ${new Date().toLocaleString('zh-TW')}`);
  
  try {
    const testResult = {
      timestamp: new Date().toISOString(),
      parseTest: null,
      integrationTest: null,
      rotationTest: null,
      apiKeyStatusTest: null,
      overallSuccess: false
    };
    
    // 測試1：Control+Enter解析修復
    console.log('\n📝 測試1：Control+Enter解析修復...');
    
    // 測試包含\r\n的字串
    const testKeyString = `AIzaSyDQ-e2UJUXibGYm9U1JvjEUEdCklROPhVA\r\nAIzaSyBZPmYl9KAPEESqn745ooMl2lG6coFFy9Q\r\nAIzaSyCHqxhLA40jPuoZ8SppFVBSyn8zNQNJltE\r\nAIzaSyDTest4thKey5forRotation6Testing7890`;
    
    const parsedKeys = parseMultipleApiKeys(testKeyString);
    
    const parseSuccess = parsedKeys.length === 4 && 
                        parsedKeys.every(key => key.startsWith('AIza') && !key.includes('\r') && !key.includes('\n'));
    
    testResult.parseTest = {
      passed: parseSuccess,
      originalLength: testKeyString.length,
      parsedCount: parsedKeys.length,
      keysValid: parsedKeys.every(key => key.startsWith('AIza')),
      noLineBreaks: parsedKeys.every(key => !key.includes('\r') && !key.includes('\n')),
      firstKey: parsedKeys[0] ? parsedKeys[0].substring(0, 20) + '...' : 'N/A',
      lastKey: parsedKeys[parsedKeys.length - 1] ? parsedKeys[parsedKeys.length - 1].substring(0, 20) + '...' : 'N/A'
    };
    
    console.log(`  解析結果: ${parseSuccess ? '✅' : '❌'}`);
    console.log(`  解析到的 Key 數量: ${parsedKeys.length}`);
    console.log(`  Key 格式正確: ${parsedKeys.every(key => key.startsWith('AIza')) ? '✅' : '❌'}`);
    console.log(`  無換行符殘留: ${parsedKeys.every(key => !key.includes('\r') && !key.includes('\n')) ? '✅' : '❌'}`);
    console.log(`  第一個 Key: ${parsedKeys[0] ? parsedKeys[0].substring(0, 20) + '...' : 'N/A'}`);
    console.log(`  最後一個 Key: ${parsedKeys[parsedKeys.length - 1] ? parsedKeys[parsedKeys.length - 1].substring(0, 20) + '...' : 'N/A'}`);
    
    // 測試2：APIKEY工作表整合
    console.log('\n📊 測試2：APIKEY工作表整合...');
    
    try {
      const ss = SpreadsheetApp.getActiveSpreadsheet();
      const apikeySheet = ss.getSheetByName('APIKEY');
      
      if (!apikeySheet) {
        throw new Error('APIKEY 工作表不存在');
      }
      
      // 檢查當前狀態
      const beforeIndex = getApiKeyRotationIndex();
      console.log(`  修復前輪換索引: ${beforeIndex}`);
      
      // 檢查行標題
      const indexTitle = apikeySheet.getRange('A5').getValue();
      const timeTitle = apikeySheet.getRange('A6').getValue();
      const currentTime = apikeySheet.getRange('B6').getValue();
      
      const titlesCorrect = indexTitle === 'Gemini API Key當前輪換索引' && 
                           timeTitle === 'Gemini API Key最後更新時間';
      
      testResult.integrationTest = {
        passed: titlesCorrect,
        indexTitle: indexTitle,
        timeTitle: timeTitle,
        beforeIndex: beforeIndex,
        lastUpdateTime: currentTime,
        sheetExists: true
      };
      
      console.log(`  索引標題: ${indexTitle === 'Gemini API Key當前輪換索引' ? '✅' : '❌'} (${indexTitle})`);
      console.log(`  時間標題: ${timeTitle === 'Gemini API Key最後更新時間' ? '✅' : '❌'} (${timeTitle})`);
      console.log(`  當前索引: ${beforeIndex}`);
      console.log(`  最後更新時間: ${currentTime}`);
      
    } catch (error) {
      console.log(`  ❌ 工作表整合測試失敗: ${error.message}`);
      testResult.integrationTest = {
        passed: false,
        error: error.message
      };
    }
    
    // 測試3：輪換功能
    console.log('\n🔄 測試3：輪換功能...');
    
    try {
      const beforeIndex = getApiKeyRotationIndex();
      const beforeTime = new Date();
      console.log(`  輪換前索引: ${beforeIndex}`);
      console.log(`  輪換前時間: ${beforeTime.toLocaleString('zh-TW')}`);
      
      // 執行輪換
      rotateToNextApiKey();
      
      const afterIndex = getApiKeyRotationIndex();
      const afterTime = new Date();
      console.log(`  輪換後索引: ${afterIndex}`);
      console.log(`  輪換後時間: ${afterTime.toLocaleString('zh-TW')}`);
      
      const rotationSuccess = afterIndex === beforeIndex + 1;
      const timeUpdated = afterTime > beforeTime;
      
      testResult.rotationTest = {
        passed: rotationSuccess && timeUpdated,
        beforeIndex: beforeIndex,
        afterIndex: afterIndex,
        indexChanged: rotationSuccess,
        timeUpdated: timeUpdated
      };
      
      console.log(`  索引輪換成功: ${rotationSuccess ? '✅' : '❌'} (${beforeIndex} → ${afterIndex})`);
      console.log(`  時間戳更新: ${timeUpdated ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`  ❌ 輪換功能測試失敗: ${error.message}`);
      testResult.rotationTest = {
        passed: false,
        error: error.message
      };
    }
    
    // 測試4：API Key 狀態檢查
    console.log('\n🔑 測試4：API Key 狀態檢查...');
    
    try {
      const keyString = getConfigValue('geminiApiKey');
      const keys = parseMultipleApiKeys(keyString);
      const currentKey = getCurrentGeminiApiKey();
      const currentIndex = getApiKeyRotationIndex();
      
      const statusTest = {
        passed: keys.length > 0 && currentKey,
        totalKeys: keys.length,
        currentKeyPreview: currentKey ? currentKey.substring(0, 20) + '...' : 'N/A',
        currentIndex: currentIndex,
        expectedIndex: currentIndex % keys.length,
        isValidIndex: currentIndex >= 0 && currentIndex < keys.length * 10, // 允許多次輪換
        keyAtIndex: keys.length > 0 ? keys[currentIndex % keys.length].substring(0, 20) + '...' : 'N/A'
      };
      
      testResult.apiKeyStatusTest = statusTest;
      
      console.log(`  總 Key 數量: ${keys.length}`);
      console.log(`  當前索引: ${currentIndex}`);
      console.log(`  當前 Key: ${currentKey ? currentKey.substring(0, 20) + '...' : 'N/A'}`);
      console.log(`  索引對應的 Key: ${keys.length > 0 ? keys[currentIndex % keys.length].substring(0, 20) + '...' : 'N/A'}`);
      console.log(`  狀態有效: ${statusTest.passed ? '✅' : '❌'}`);
      
    } catch (error) {
      console.log(`  ❌ API Key 狀態檢查失敗: ${error.message}`);
      testResult.apiKeyStatusTest = {
        passed: false,
        error: error.message
      };
    }
    
    // 測試5：檢查舊工作表是否已移除
    console.log('\n🧹 測試5：舊工作表清理檢查...');
    
    try {
      const ss = SpreadsheetApp.getActiveSpreadsheet();
      const statusSheet = ss.getSheetByName('API_KEY_STATUS');
      
      const cleanupSuccess = !statusSheet;
      
      if (cleanupSuccess) {
        console.log(`  ✅ API_KEY_STATUS 工作表已正確移除`);
      } else {
        console.log(`  ⚠️ 警告：API_KEY_STATUS 工作表仍然存在，應該已被移除`);
      }
      
    } catch (error) {
      console.log(`  ❌ 清理檢查失敗: ${error.message}`);
    }
    
    // 評估整體結果
    const allTestsPassed = testResult.parseTest?.passed && 
                          testResult.integrationTest?.passed && 
                          testResult.rotationTest?.passed &&
                          testResult.apiKeyStatusTest?.passed;
    
    testResult.overallSuccess = allTestsPassed;
    
    console.log('\n📋 測試結果總結:');
    console.log(`  Control+Enter解析: ${testResult.parseTest?.passed ? '✅' : '❌'}`);
    console.log(`  APIKEY工作表整合: ${testResult.integrationTest?.passed ? '✅' : '❌'}`);
    console.log(`  輪換功能: ${testResult.rotationTest?.passed ? '✅' : '❌'}`);
    console.log(`  API Key 狀態: ${testResult.apiKeyStatusTest?.passed ? '✅' : '❌'}`);
    console.log(`  整體成功: ${allTestsPassed ? '✅' : '❌'}`);
    
    if (allTestsPassed) {
      console.log('\n🎉 API Key輪換修復成功！');
      console.log('✅ 支援 Control+Enter 換行分隔的 API Key');
      console.log('✅ 狀態追蹤整合到 APIKEY 工作表');
      console.log('✅ 無換行符殘留問題');
      console.log('✅ 輪換功能正常工作');
      console.log('✅ API Key 狀態檢查正常');
      console.log('✅ 舊工作表已正確清理');
    } else {
      console.log('\n❌ 修復未完全成功，請檢查失敗的測試項目');
    }
    
    console.log('\n💡 使用說明:');
    console.log('1. 在 APIKEY 工作表的 "Gemini API Key" 欄位（B4）設定多組 Key');
    console.log('2. 使用 Control+Enter 換行分隔，每行一組 Key');
    console.log('3. 系統會自動在 A5/B5 和 A6/B6 管理輪換狀態');
    console.log('4. 遇到 429 錯誤時自動輪換到下一組 Key');
    console.log('5. 輪換會更新 APIKEY 工作表的當前索引和時間戳');
    
    console.log('\n📊 429 錯誤處理邏輯:');
    console.log('• callGemini() 函數：檢測 429 → 輪換 → 重試一次');
    console.log('• callGeminiAudioDialog() 函數：檢測 429 → 輪換 → 重試一次');
    console.log('• 如果輪換後仍失敗，返回「所有 API Key 配額已用完」錯誤');
    console.log('• 輪換索引會持續增加，使用 index % keys.length 來循環使用');
    
    console.log(`\n⏰ 測試結束時間: ${new Date().toLocaleString('zh-TW')}`);
    console.log('🎉 ===== API Key輪換修復驗證完成 =====');
    
    return testResult;
    
  } catch (error) {
    console.error('❌ API Key輪換修復驗證失敗:', error);
    return {
      overallSuccess: false,
      error: error.message
    };
  }
}

/**
 * 🚨 模擬 429 錯誤測試
 * 測試當所有 API Key 都遇到 429 錯誤時的行為
 */
function 模擬429錯誤測試_debug() {
  console.log('🚨 ===== 模擬 429 錯誤測試開始 =====');
  
  try {
    // 記錄當前狀態
    const beforeIndex = getApiKeyRotationIndex();
    const keyString = getConfigValue('geminiApiKey');
    const keys = parseMultipleApiKeys(keyString);
    
    console.log(`📊 測試前狀態:`);
    console.log(`  當前索引: ${beforeIndex}`);
    console.log(`  總 Key 數量: ${keys.length}`);
    console.log(`  當前使用的 Key: ${keys[beforeIndex % keys.length] ? keys[beforeIndex % keys.length].substring(0, 20) + '...' : 'N/A'}`);
    
    // 模擬多次 429 錯誤（輪換所有 Key）
    console.log('\n🔄 模擬多次 429 錯誤，測試完整輪換...');
    
    for (let i = 0; i < keys.length + 1; i++) {
      const currentIndex = getApiKeyRotationIndex();
      const currentKey = getCurrentGeminiApiKey();
      
      console.log(`\n第 ${i + 1} 次模擬 429 錯誤:`);
      console.log(`  當前索引: ${currentIndex}`);
      console.log(`  當前 Key: ${currentKey.substring(0, 20)}...`);
      
      // 模擬 429 錯誤處理
      console.log(`  檢測到 429 錯誤，執行輪換...`);
      rotateToNextApiKey();
      
      const afterIndex = getApiKeyRotationIndex();
      const afterKey = getCurrentGeminiApiKey();
      
      console.log(`  輪換後索引: ${afterIndex}`);
      console.log(`  輪換後 Key: ${afterKey.substring(0, 20)}...`);
      console.log(`  輪換成功: ${afterIndex === currentIndex + 1 ? '✅' : '❌'}`);
      
      // 短暫延遲
      Utilities.sleep(100);
    }
    
    const finalIndex = getApiKeyRotationIndex();
    const finalKey = getCurrentGeminiApiKey();
    
    console.log('\n📊 測試後狀態:');
    console.log(`  最終索引: ${finalIndex}`);
    console.log(`  最終 Key: ${finalKey.substring(0, 20)}...`);
    console.log(`  總輪換次數: ${finalIndex - beforeIndex}`);
    console.log(`  已回到第一個 Key: ${finalIndex % keys.length === (beforeIndex + keys.length + 1) % keys.length ? '✅' : '❌'}`);
    
    console.log('\n💡 結論:');
    console.log('• 每次 429 錯誤都會觸發輪換到下一個 Key');
    console.log('• 輪換索引持續增加，使用取模運算循環使用 Key');
    console.log('• 如果所有 Key 都遇到 429 錯誤，系統會返回「配額已用完」錯誤');
    console.log('• 輪換機制可以有效分散 API 調用，避免單個 Key 過度使用');
    
    console.log('\n🎉 ===== 模擬 429 錯誤測試完成 =====');
    
  } catch (error) {
    console.error('❌ 模擬 429 錯誤測試失敗:', error);
  }
}

/**
 * 🔍 API Key 輪換狀態檢查
 * 快速檢查當前輪換狀態
 */
function 檢查API_Key輪換狀態_debug() {
  console.log('🔍 ===== API Key 輪換狀態檢查 =====');
  
  try {
    const keyString = getConfigValue('geminiApiKey');
    const keys = parseMultipleApiKeys(keyString);
    const currentIndex = getApiKeyRotationIndex();
    const currentKey = getCurrentGeminiApiKey();
    
    console.log(`📊 當前狀態:`);
    console.log(`  配置的 Key 數量: ${keys.length}`);
    console.log(`  當前輪換索引: ${currentIndex}`);
    console.log(`  實際使用索引: ${currentIndex % keys.length}`);
    console.log(`  當前使用的 Key: ${currentKey.substring(0, 20)}...`);
    
    console.log('\n🔑 所有 Key 列表:');
    keys.forEach((key, index) => {
      const isCurrent = index === currentIndex % keys.length;
      console.log(`  ${index + 1}. ${key.substring(0, 20)}... ${isCurrent ? '← 當前使用' : ''}`);
    });
    
    // 檢查 APIKEY 工作表狀態
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (apikeySheet) {
      const lastUpdateTime = apikeySheet.getRange('B6').getValue();
      console.log(`\n📅 最後更新時間: ${lastUpdateTime}`);
      
      const timeDiff = new Date() - new Date(lastUpdateTime);
      const minutesAgo = Math.floor(timeDiff / 60000);
      console.log(`  距離現在: ${minutesAgo} 分鐘前`);
    }
    
    console.log('\n💡 說明:');
    console.log('• 索引會持續增加，使用取模運算循環使用 Key');
    console.log('• 每次 429 錯誤都會觸發輪換到下一個 Key');
    console.log('• 輪換會更新 APIKEY 工作表的時間戳');
    
    console.log('\n🎉 ===== 檢查完成 =====');
    
  } catch (error) {
    console.error('❌ API Key 輪換狀態檢查失敗:', error);
  }
}

/**
 * 🧪 快速驗證_debug
 * 快速檢查修復狀態
 */
function 快速驗證_debug() {
  console.log('🧪 ===== 快速驗證開始 =====');
  
  try {
    // 檢查解析功能
    const testKeys = parseMultipleApiKeys('key1\r\nkey2\r\nkey3');
    console.log(`解析測試: ${testKeys.length === 3 ? '✅' : '❌'} (${testKeys.length} 組)`);
    
    // 檢查工作表整合
    const currentIndex = getApiKeyRotationIndex();
    console.log(`索引讀取: ✅ (當前索引: ${currentIndex})`);
    
    // 檢查API_KEY_STATUS工作表是否存在
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const statusSheet = ss.getSheetByName('API_KEY_STATUS');
    console.log(`API_KEY_STATUS移除: ${statusSheet ? '❌ 仍存在' : '✅ 已移除'}`);
    
    // 檢查當前 Key 狀態
    const currentKey = getCurrentGeminiApiKey();
    console.log(`當前 Key 獲取: ${currentKey ? '✅' : '❌'} (${currentKey ? currentKey.substring(0, 20) + '...' : 'N/A'})`);
    
    console.log('\n🎉 ===== 快速驗證完成 =====');
    
  } catch (error) {
    console.error('❌ 快速驗證失敗:', error);
  }
}
