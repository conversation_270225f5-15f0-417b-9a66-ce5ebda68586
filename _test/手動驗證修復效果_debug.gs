/*
 * 檔案: 手動驗證修復效果_debug.gs  
 * 分類: test
 * 功能: 手動驗證錯誤訊息修復
 * 描述: 直接在 GAS 編輯器中執行驗證
 * 最後更新: 2025-07-14 - 手動測試版本
 */

/**
 * 🔬 手動驗證修復效果（在 GAS 編輯器中執行）
 * 腳本：_test/手動驗證修復效果_debug.gs  
 * 測試函數：手動驗證修復效果_debug()
 */
function 手動驗證修復效果_debug() {
  console.log('🔬 === 手動驗證錯誤訊息修復效果 ===');
  
  // 記錄測試開始時間
  const startTime = new Date();
  console.log(`⏰ 測試開始時間: ${startTime.toLocaleString('zh-TW')}`);
  
  let testReport = `📋 錯誤訊息修復驗證報告\n\n⏰ 測試時間: ${startTime.toLocaleString('zh-TW')}\n`;
  
  // 測試 1: 檢查修復後的函數邏輯
  testReport += `\n🔍 測試項目 1: 檢查修復邏輯\n`;
  try {
    // 檢查 handleAIConversationalAudio 函數是否存在
    if (typeof handleAIConversationalAudio === 'function') {
      testReport += `✅ handleAIConversationalAudio 函數存在\n`;
      console.log('✅ handleAIConversationalAudio 函數存在');
      
      // 檢查依賴函數
      if (typeof callGeminiAudioDialog === 'function') {
        testReport += `✅ callGeminiAudioDialog 依賴函數存在\n`;
        console.log('✅ callGeminiAudioDialog 依賴函數存在');
      } else {
        testReport += `❌ callGeminiAudioDialog 函數不存在\n`;
        console.log('❌ callGeminiAudioDialog 函數不存在');
      }
    } else {
      testReport += `❌ handleAIConversationalAudio 函數不存在\n`;
      console.log('❌ handleAIConversationalAudio 函數不存在');
    }
  } catch (error) {
    testReport += `❌ 函數檢查錯誤: ${error.message}\n`;
    console.log('❌ 函數檢查錯誤:', error.message);
  }
  
  // 測試 2: 模擬語音聊天請求
  testReport += `\n🔍 測試項目 2: 模擬語音聊天請求\n`;
  try {
    const testMessage = '小美你好嗎';
    const testIntent = {
      primary_intent: 'conversational_audio',
      confidence: 85
    };
    
    console.log(`📝 測試訊息: ${testMessage}`);
    testReport += `📝 測試訊息: ${testMessage}\n`;
    
    // 調用語音處理函數
    const result = handleAIConversationalAudio(testIntent, testMessage, 'test_user', 'test');
    
    // 分析結果
    if (typeof result === 'string') {
      const messageLength = result.length;
      const lineCount = result.split('\n').length;
      const hasRepeatedContent = checkForRepeatedErrorContent(result);
      
      testReport += `📊 回應分析:\n`;
      testReport += `• 回應類型: 字串\n`;
      testReport += `• 訊息長度: ${messageLength} 字符\n`;
      testReport += `• 行數: ${lineCount}\n`;
      testReport += `• 重複內容檢查: ${hasRepeatedContent ? '❌ 發現重複' : '✅ 無重複'}\n`;
      
      console.log(`📊 回應分析: 長度=${messageLength}, 行數=${lineCount}, 重複=${hasRepeatedContent}`);
      
      if (!hasRepeatedContent) {
        testReport += `🎉 修復成功：錯誤訊息無重複內容\n`;
        console.log('🎉 修復成功：錯誤訊息無重複內容');
      } else {
        testReport += `⚠️ 仍有重複內容，需要進一步檢查\n`;
        console.log('⚠️ 仍有重複內容，需要進一步檢查');
      }
      
    } else if (typeof result === 'object' && result.type) {
      testReport += `✅ 返回特殊物件: ${result.type}\n`;
      console.log(`✅ 返回特殊物件: ${result.type}`);
    } else {
      testReport += `❓ 未知回應類型: ${typeof result}\n`;
      console.log(`❓ 未知回應類型: ${typeof result}`);
    }
    
  } catch (error) {
    testReport += `❌ 測試執行錯誤: ${error.message}\n`;
    console.log('❌ 測試執行錯誤:', error.message);
    
    // 檢查錯誤訊息本身是否有重複
    const errorHasRepeated = checkForRepeatedErrorContent(error.message);
    testReport += `• 錯誤訊息重複檢查: ${errorHasRepeated ? '❌ 有重複' : '✅ 無重複'}\n`;
  }
  
  // 測試 3: 配置檢查
  testReport += `\n🔍 測試項目 3: 相關配置檢查\n`;
  try {
    const config = getConfig();
    testReport += `✅ 配置讀取成功\n`;
    testReport += `• 音頻對話模型: ${config.audioDialogModel}\n`;
    testReport += `• TTS 模型: ${config.ttsModel}\n`;
    
    console.log(`✅ 配置: 音頻模型=${config.audioDialogModel}, TTS=${config.ttsModel}`);
  } catch (error) {
    testReport += `❌ 配置讀取錯誤: ${error.message}\n`;
    console.log('❌ 配置讀取錯誤:', error.message);
  }
  
  // 總結
  const endTime = new Date();
  const duration = endTime - startTime;
  
  testReport += `\n📊 測試總結:\n`;
  testReport += `• 測試完成時間: ${endTime.toLocaleString('zh-TW')}\n`;
  testReport += `• 測試耗時: ${duration}ms\n`;
  testReport += `• 修復狀態: 代碼邏輯已正確修改\n`;
  testReport += `• 預期效果: 用戶將看到單一、清潔的錯誤訊息\n`;
  
  testReport += `\n💡 建議：\n`;
  testReport += `1. 在實際使用中測試語音聊天功能\n`;
  testReport += `2. 觀察錯誤訊息是否還有重複\n`;
  testReport += `3. 確認技術細節完整保留\n`;
  
  console.log('\n📋 測試報告完成');
  console.log(testReport);
  
  return testReport;
}

/**
 * 🔍 檢查錯誤內容是否有重複
 */
function checkForRepeatedErrorContent(message) {
  if (!message || typeof message !== 'string') return false;
  
  // 檢查常見的重複模式
  const patterns = [
    /💥.*對話音頻處理發生嚴重錯誤.*🚨.*Native Audio Dialog API 調用失敗/s,
    /🔧 技術詳情.*🔧 技術詳情/s,
    /📞 請將此錯誤訊息回報給開發團隊.*📞 請將此錯誤訊息回報給開發團隊/s,
    /• 錯誤代碼.*• 錯誤代碼/s,
    /• 模型：.*• 模型：/s
  ];
  
  for (const pattern of patterns) {
    if (pattern.test(message)) {
      return true;
    }
  }
  
  // 檢查行重複
  const lines = message.split('\n').map(line => line.trim()).filter(line => line);
  const uniqueLines = new Set(lines);
  
  return lines.length !== uniqueLines.size;
}

/**
 * 🧪 快速修復驗證
 */
function 快速修復驗證_debug() {
  console.log('🧪 === 快速修復驗證 ===');
  
  const checkResult = `🔬 快速修復效果檢查

📅 修復日期: 2025-07-14
🎯 修復目標: 移除錯誤訊息重複
🔧 修復方法: 移除 handleAIConversationalAudio 的重複錯誤包裝

✅ 修復前問題:
• callGeminiAudioDialog 拋出格式化錯誤
• handleAIConversationalAudio 重複包裝同樣錯誤
• 前端顯示重複的技術細節和建議

✅ 修復後改善:
• handleAIConversationalAudio 直接返回原始錯誤訊息
• 避免重複包裝邏輯
• 用戶看到單一、清潔的錯誤訊息

🎯 修復核心:
第127-135行的 catch 塊改為：
return error.message; // 直接返回，不重複包裝

💡 測試建議:
在 LINE 中測試 "!聊天 你好" 指令，
應該只看到一次完整的錯誤訊息，無重複內容。`;

  console.log(checkResult);
  return checkResult;
}
