// _test/語音修復驗證_debug.gs
// @last-update 2025-01-14
// @description 驗證語音功能修復效果

/**
 * 🧪 測試Audio Dialog修復
 */
function 測試AudioDialog修復_debug() {
  console.log('🧪 ===== 測試Audio Dialog修復 =====');
  
  try {
    const testMessage = '你好，請用語音回覆我';
    console.log(`📝 測試訊息: ${testMessage}`);
    
    // 測試對話音頻調用
    const result = callGeminiAudioDialog(testMessage, {});
    
    console.log('📊 調用結果:');
    console.log(`• 成功: ${result.success}`);
    console.log(`• 模型: ${result.modelUsed || 'unknown'}`);
    console.log(`• 錯誤: ${result.error || 'none'}`);
    
    if (result.success) {
      console.log('✅ Audio Dialog 修復成功！');
      return '✅ Audio Dialog 功能正常';
    } else {
      console.log('❌ Audio Dialog 仍有問題');
      return `❌ Audio Dialog 錯誤: ${result.error}`;
    }
    
  } catch (error) {
    console.error('❌ 測試過程發生錯誤:', error);
    return `❌ 測試錯誤: ${error.message}`;
  }
}

/**
 * 🧪 測試TTS修復
 */
function 測試TTS修復_debug() {
  console.log('🧪 ===== 測試TTS修復 =====');
  
  try {
    const testText = '蚵仔煎你在嗎？';
    console.log(`📝 測試文字: ${testText}`);
    
    // 測試TTS調用
    const result = textToSpeechWithGemini(testText);
    
    console.log('📊 調用結果:');
    console.log(`• 成功: ${result.success}`);
    console.log(`• 錯誤: ${result.error || 'none'}`);
    console.log(`• 可播放: ${result.isPlayable}`);
    
    if (result.success) {
      console.log('✅ TTS 修復成功！');
      return '✅ TTS 功能正常';
    } else {
      console.log('❌ TTS 仍有問題');
      return `❌ TTS 錯誤: ${result.error}`;
    }
    
  } catch (error) {
    console.error('❌ 測試過程發生錯誤:', error);
    return `❌ 測試錯誤: ${error.message}`;
  }
}

/**
 * 🧪 完整語音功能測試
 */
function 完整語音功能測試_debug() {
  console.log('🧪 ===== 完整語音功能測試 =====');
  
  const results = {
    audioDialog: null,
    tts: null,
    timestamp: new Date().toLocaleString('zh-TW')
  };
  
  // 測試Audio Dialog
  console.log('\n📋 1. 測試Audio Dialog...');
  results.audioDialog = 測試AudioDialog修復_debug();
  
  // 測試TTS
  console.log('\n📋 2. 測試TTS...');
  results.tts = 測試TTS修復_debug();
  
  // 總結
  console.log('\n📊 ===== 測試總結 =====');
  console.log(`⏰ 測試時間: ${results.timestamp}`);
  console.log(`🎙️ Audio Dialog: ${results.audioDialog}`);
  console.log(`🔊 TTS: ${results.tts}`);
  
  const audioDialogOk = results.audioDialog.includes('✅');
  const ttsOk = results.tts.includes('✅');
  
  if (audioDialogOk && ttsOk) {
    console.log('🎉 所有語音功能修復成功！');
    return '🎉 語音功能完全修復';
  } else if (audioDialogOk || ttsOk) {
    console.log('⚠️ 部分語音功能修復成功');
    return '⚠️ 部分功能修復，需要進一步調整';
  } else {
    console.log('❌ 語音功能仍需修復');
    return '❌ 需要進一步修復';
  }
}

/**
 * 🔍 檢查模型配置
 */
function 檢查模型配置_debug() {
  console.log('🔍 ===== 檢查模型配置 =====');
  
  try {
    const config = getConfig();
    
    console.log('📊 當前模型配置:');
    console.log(`• TTS模型: ${config.ttsModel}`);
    console.log(`• 對話音頻模型: ${config.audioDialogModel}`);
    
    // 檢查API版本
    const ttsApiVersion = getModelApiVersion(config.ttsModel);
    const audioApiVersion = getModelApiVersion(config.audioDialogModel);
    
    console.log('📊 API版本:');
    console.log(`• TTS API版本: ${ttsApiVersion}`);
    console.log(`• 對話音頻API版本: ${audioApiVersion}`);
    
    return {
      ttsModel: config.ttsModel,
      audioDialogModel: config.audioDialogModel,
      ttsApiVersion,
      audioApiVersion
    };
    
  } catch (error) {
    console.error('❌ 檢查配置失敗:', error);
    return { error: error.message };
  }
}
