// _test/統一API輪換機制測試_debug.gs
// @last-update 2025-01-14
// @description 測試統一API Key輪換包裝器的功能

/**
 * 🧪 統一API輪換機制完整測試
 * 測試所有重構後的API調用函數
 */
function 統一API輪換機制完整測試_debug() {
  console.log('🧪 ===== 統一API輪換機制完整測試開始 =====');
  console.log(`⏰ 測試開始時間: ${new Date().toLocaleString('zh-TW')}`);
  
  const testResults = {
    timestamp: new Date().toISOString(),
    wrapperTest: null,
    geminiTextTest: null,
    audioDialogTest: null,
    ttsTest: null,
    keyRotationTest: null,
    overallSuccess: false
  };
  
  try {
    // 測試1：統一包裝器基本功能
    console.log('\n📋 測試1：統一包裝器基本功能...');
    testResults.wrapperTest = testApiWrapper();
    
    // 測試2：Gemini文字API
    console.log('\n📋 測試2：Gemini文字API...');
    testResults.geminiTextTest = testGeminiTextAPI();
    
    // 測試3：對話音頻API
    console.log('\n📋 測試3：對話音頻API...');
    testResults.audioDialogTest = testAudioDialogAPI();
    
    // 測試4：TTS API
    console.log('\n📋 測試4：TTS API...');
    testResults.ttsTest = testTTSAPI();
    
    // 測試5：KEY輪換機制
    console.log('\n📋 測試5：KEY輪換機制...');
    testResults.keyRotationTest = testKeyRotationMechanism();
    
    // 評估整體結果
    const successCount = Object.values(testResults).filter(result => 
      result && typeof result === 'object' && result.passed
    ).length;
    
    testResults.overallSuccess = successCount >= 4; // 至少4個測試通過
    
    console.log('\n📊 ===== 測試結果總結 =====');
    console.log(`✅ 包裝器測試: ${testResults.wrapperTest?.passed ? '通過' : '失敗'}`);
    console.log(`✅ 文字API測試: ${testResults.geminiTextTest?.passed ? '通過' : '失敗'}`);
    console.log(`✅ 音頻對話測試: ${testResults.audioDialogTest?.passed ? '通過' : '失敗'}`);
    console.log(`✅ TTS測試: ${testResults.ttsTest?.passed ? '通過' : '失敗'}`);
    console.log(`✅ KEY輪換測試: ${testResults.keyRotationTest?.passed ? '通過' : '失敗'}`);
    console.log(`🎯 整體結果: ${testResults.overallSuccess ? '✅ 成功' : '❌ 失敗'}`);
    
    console.log(`⏰ 測試結束時間: ${new Date().toLocaleString('zh-TW')}`);
    console.log('🎉 ===== 統一API輪換機制測試完成 =====');
    
    return testResults;
    
  } catch (error) {
    console.error('❌ 測試過程發生錯誤:', error);
    testResults.overallSuccess = false;
    testResults.error = error.message;
    return testResults;
  }
}

/**
 * 🔧 測試統一包裝器基本功能
 */
function testApiWrapper() {
  try {
    console.log('🔧 測試統一包裝器基本功能...');
    
    // 檢查包裝器函數是否存在
    if (typeof callApiWithRetry !== 'function') {
      return { passed: false, error: 'callApiWithRetry 函數不存在' };
    }
    
    if (typeof getTotalApiKeys !== 'function') {
      return { passed: false, error: 'getTotalApiKeys 函數不存在' };
    }
    
    if (typeof isQuotaError !== 'function') {
      return { passed: false, error: 'isQuotaError 函數不存在' };
    }
    
    // 測試輔助函數
    const totalKeys = getTotalApiKeys();
    console.log(`📊 檢測到 ${totalKeys} 組API Key`);
    
    // 測試錯誤檢測
    const isQuota1 = isQuotaError('429 - Resource has been exhausted');
    const isQuota2 = isQuotaError('Normal error message');
    
    console.log(`🔍 429錯誤檢測: ${isQuota1 ? '✅' : '❌'}`);
    console.log(`🔍 普通錯誤檢測: ${!isQuota2 ? '✅' : '❌'}`);
    
    return {
      passed: totalKeys > 0 && isQuota1 && !isQuota2,
      totalKeys: totalKeys,
      quotaDetection: isQuota1 && !isQuota2
    };
    
  } catch (error) {
    console.error('❌ 包裝器測試失敗:', error);
    return { passed: false, error: error.message };
  }
}

/**
 * 📝 測試Gemini文字API
 */
function testGeminiTextAPI() {
  try {
    console.log('📝 測試Gemini文字API...');
    
    // 檢查函數是否存在
    if (typeof callGemini !== 'function') {
      return { passed: false, error: 'callGemini 函數不存在' };
    }
    
    if (typeof callGeminiDirect !== 'function') {
      return { passed: false, error: 'callGeminiDirect 函數不存在' };
    }
    
    // 簡單測試調用
    const testPrompt = '請回答：1+1等於多少？只回答數字。';
    const startTime = Date.now();
    
    const result = callGemini(testPrompt, 'general');
    const responseTime = Date.now() - startTime;
    
    console.log(`📝 測試提示: ${testPrompt}`);
    console.log(`📝 API回應: ${result.substring(0, 50)}...`);
    console.log(`⏱️ 回應時間: ${responseTime}ms`);
    
    const isValidResponse = result && result.length > 0;
    
    return {
      passed: isValidResponse,
      responseTime: responseTime,
      responseLength: result.length,
      responsePreview: result.substring(0, 100)
    };
    
  } catch (error) {
    console.error('❌ Gemini文字API測試失敗:', error);
    return { passed: false, error: error.message };
  }
}

/**
 * 🎙️ 測試對話音頻API
 */
function testAudioDialogAPI() {
  try {
    console.log('🎙️ 測試對話音頻API...');
    
    // 檢查函數是否存在
    if (typeof callGeminiAudioDialog !== 'function') {
      return { passed: false, error: 'callGeminiAudioDialog 函數不存在' };
    }
    
    if (typeof callGeminiAudioDialogDirect !== 'function') {
      return { passed: false, error: 'callGeminiAudioDialogDirect 函數不存在' };
    }
    
    console.log('🎙️ 對話音頻API函數存在，跳過實際調用測試（避免消耗配額）');
    
    return {
      passed: true,
      note: '函數存在性檢查通過，跳過實際API調用'
    };
    
  } catch (error) {
    console.error('❌ 對話音頻API測試失敗:', error);
    return { passed: false, error: error.message };
  }
}

/**
 * 🔊 測試TTS API
 */
function testTTSAPI() {
  try {
    console.log('🔊 測試TTS API...');
    
    // 檢查函數是否存在
    if (typeof textToSpeechWithGemini !== 'function') {
      return { passed: false, error: 'textToSpeechWithGemini 函數不存在' };
    }
    
    if (typeof textToSpeechWithGeminiDirect !== 'function') {
      return { passed: false, error: 'textToSpeechWithGeminiDirect 函數不存在' };
    }
    
    console.log('🔊 TTS API函數存在，跳過實際調用測試（避免消耗配額）');
    
    return {
      passed: true,
      note: '函數存在性檢查通過，跳過實際API調用'
    };
    
  } catch (error) {
    console.error('❌ TTS API測試失敗:', error);
    return { passed: false, error: error.message };
  }
}

/**
 * 🔄 測試KEY輪換機制
 */
function testKeyRotationMechanism() {
  try {
    console.log('🔄 測試KEY輪換機制...');
    
    // 檢查輪換相關函數
    if (typeof rotateToNextApiKey !== 'function') {
      return { passed: false, error: 'rotateToNextApiKey 函數不存在' };
    }
    
    if (typeof getCurrentGeminiApiKey !== 'function') {
      return { passed: false, error: 'getCurrentGeminiApiKey 函數不存在' };
    }
    
    if (typeof getApiKeyRotationIndex !== 'function') {
      return { passed: false, error: 'getApiKeyRotationIndex 函數不存在' };
    }
    
    // 記錄當前狀態
    const initialIndex = getApiKeyRotationIndex();
    const initialKey = getCurrentGeminiApiKey();
    
    console.log(`🔄 初始索引: ${initialIndex}`);
    console.log(`🔄 初始Key: ${initialKey.substring(0, 20)}...`);
    
    // 執行一次輪換
    rotateToNextApiKey();
    
    const afterIndex = getApiKeyRotationIndex();
    const afterKey = getCurrentGeminiApiKey();
    
    console.log(`🔄 輪換後索引: ${afterIndex}`);
    console.log(`🔄 輪換後Key: ${afterKey.substring(0, 20)}...`);
    
    const rotationWorked = afterIndex === initialIndex + 1;
    
    return {
      passed: rotationWorked,
      initialIndex: initialIndex,
      afterIndex: afterIndex,
      rotationWorked: rotationWorked
    };
    
  } catch (error) {
    console.error('❌ KEY輪換機制測試失敗:', error);
    return { passed: false, error: error.message };
  }
}

/**
 * 🚀 快速驗證統一包裝器
 * 簡化版測試，只檢查關鍵功能
 */
function 快速驗證統一包裝器_debug() {
  console.log('🚀 ===== 快速驗證統一包裝器 =====');
  
  try {
    // 檢查核心函數存在性
    const functions = [
      'callApiWithRetry',
      'getTotalApiKeys', 
      'isQuotaError',
      'callGemini',
      'callGeminiDirect',
      'callGeminiAudioDialog',
      'callGeminiAudioDialogDirect',
      'textToSpeechWithGemini',
      'textToSpeechWithGeminiDirect'
    ];
    
    console.log('🔍 檢查函數存在性:');
    functions.forEach(funcName => {
      const exists = typeof eval(funcName) === 'function';
      console.log(`  ${funcName}: ${exists ? '✅' : '❌'}`);
    });
    
    // 檢查API Key配置
    const totalKeys = getTotalApiKeys();
    console.log(`📊 API Key數量: ${totalKeys}`);
    
    // 測試錯誤檢測
    const quotaTest = isQuotaError('429 - quota exceeded');
    console.log(`🔍 配額錯誤檢測: ${quotaTest ? '✅' : '❌'}`);
    
    console.log('✅ 快速驗證完成');
    
  } catch (error) {
    console.error('❌ 快速驗證失敗:', error);
  }
}
