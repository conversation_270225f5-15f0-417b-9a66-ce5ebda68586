/*
 * 檔案: simple_test_debug.gs
 * 簡單測試函數 - 英文名稱
 */

/**
 * 簡單測試函數 - 使用英文名稱避免編碼問題
 */
function simpleTest_debug() {
  console.log('🧪 === 簡單測試開始 ===');
  
  try {
    // 測試基本功能
    const timestamp = new Date().toISOString();
    console.log(`⏰ 測試時間: ${timestamp}`);
    
    // 測試配置讀取
    const config = getConfig();
    const hasGeminiKey = !!config.geminiApiKey;
    console.log(`🔑 Gemini API Key 存在: ${hasGeminiKey}`);
    
    // 測試錯誤訊息修復相關函數
    const functions = [
      'handleAIConversationalAudio',
      'callGeminiAudioDialog',
      'logActivity'
    ];
    
    let results = '📊 函數可用性檢查:\n';
    let available = 0;
    
    for (const funcName of functions) {
      try {
        const func = eval(funcName);
        if (typeof func === 'function') {
          results += `✅ ${funcName}: 可用\n`;
          available++;
        } else {
          results += `❌ ${funcName}: 不是函數\n`;
        }
      } catch (error) {
        results += `❌ ${funcName}: ${error.message}\n`;
      }
    }
    
    results += `\n📈 可用率: ${available}/${functions.length} (${((available/functions.length)*100).toFixed(1)}%)`;
    
    const report = `✅ 簡單測試完成

⏰ 執行時間: ${timestamp}
🔧 基本配置: ${hasGeminiKey ? '正常' : '需要檢查'}

${results}

🎯 錯誤訊息修復狀態: 
- 修復已提交 ✅
- 函數可訪問性 ${available >= 2 ? '✅' : '❌'}
- 測試腳本運行 ✅

💡 下一步: 測試實際的語音錯誤處理流程`;

    console.log(report);
    return report;
    
  } catch (error) {
    const errorReport = `❌ 簡單測試失敗: ${error.message}`;
    console.log(errorReport);
    return errorReport;
  }
}

/**
 * 測試錯誤訊息修復 - 英文函數名
 */
function testErrorMessageFix_debug() {
  console.log('🧪 === 測試錯誤訊息修復 ===');
  
  try {
    // 模擬語音聊天請求
    const testMessage = '小美你好嗎';
    const intent = {
      primary_intent: 'conversational_audio',
      confidence: 85
    };
    
    console.log(`📝 測試訊息: ${testMessage}`);
    
    // 調用語音處理函數
    const result = handleAIConversationalAudio(intent, testMessage, 'test_user', 'user');
    
    // 分析結果
    let analysis = '';
    
    if (typeof result === 'string') {
      const lineCount = result.split('\n').length;
      const hasRepeat = checkForRepeatedContent(result);
      
      analysis = `📊 結果分析:
• 回應類型: 文字字串
• 行數: ${lineCount}
• 重複內容: ${hasRepeat ? '發現重複' : '無重複'}
• 長度: ${result.length} 字符
• 修復狀態: ${hasRepeat ? '❌ 仍有問題' : '✅ 修復成功'}`;

    } else if (typeof result === 'object') {
      analysis = `📊 結果分析:
• 回應類型: 物件
• 物件類型: ${result.type || '未知'}
• 修復狀態: ✅ 功能正常`;
    } else {
      analysis = `📊 結果分析:
• 回應類型: ${typeof result}
• 修復狀態: ❓ 需進一步檢查`;
    }
    
    const report = `🎯 錯誤訊息修復測試報告

⏰ 測試時間: ${new Date().toLocaleString('zh-TW')}
📝 測試場景: 語音聊天請求

${analysis}

💡 結論: ${analysis.includes('修復成功') ? '✅ 錯誤訊息重複問題已解決' : '🔍 需要進一步調查'}`;

    console.log(report);
    return report;
    
  } catch (error) {
    const errorReport = `❌ 錯誤訊息修復測試失敗: ${error.message}`;
    console.log(errorReport);
    return errorReport;
  }
}

/**
 * 檢查文字中是否有重複內容
 */
function checkForRepeatedContent(text) {
  const lines = text.split('\n').map(line => line.trim()).filter(line => line);
  const seen = new Set();
  
  for (const line of lines) {
    if (seen.has(line)) {
      return true;
    }
    seen.add(line);
  }
  
  return false;
}
