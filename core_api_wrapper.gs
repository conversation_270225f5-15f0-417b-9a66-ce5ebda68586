// core_api_wrapper.gs
// @last-update 2025-01-14
// @description 統一API Key輪換包裝器 - 讓所有API調用都能使用同一套KEY輪換機制
// @dependencies core_utils.gs (rotateToNextApiKey, getCurrentGeminiApiKey, parseMultipleApiKeys, getConfigValue)

/**
 * 🔄 統一API調用包裝器 - 支援自動KEY輪換和重試
 * 每換一個KEY就重試一次，全部KEY失敗後返回「所有 API Key 配額已用完」錯誤
 * 
 * @param {Function} apiFunction - 要執行的API調用函數
 * @param {...any} args - 傳遞給API函數的參數
 * @returns {any} API調用結果
 * @throws {Error} 當所有API Key都配額用完時拋出錯誤
 */
function callApiWithRetry(apiFunction, ...args) {
  console.log(`🔄 開始統一API調用: ${apiFunction.name || 'anonymous'}`);
  
  const totalKeys = getTotalApiKeys();
  const maxRetries = totalKeys; // 每個KEY重試一次
  let currentRetry = 0;
  let lastError = null;
  
  console.log(`📊 總共有 ${totalKeys} 組API Key，最多重試 ${maxRetries} 次`);
  
  while (currentRetry < maxRetries) {
    try {
      console.log(`🎯 第 ${currentRetry + 1} 次嘗試 (使用第 ${(getApiKeyRotationIndex() % totalKeys) + 1} 組KEY)`);
      
      // 執行API調用
      const result = apiFunction(...args);
      
      console.log(`✅ API調用成功 (第 ${currentRetry + 1} 次嘗試)`);
      return result;
      
    } catch (error) {
      lastError = error;
      console.log(`❌ API調用失敗 (第 ${currentRetry + 1} 次嘗試): ${error.message || error}`);

      // 🔧 修復：優先檢查權限錯誤，不要誤判為配額錯誤
      const errorString = error?.message || error?.toString() || String(error);

      // 檢查是否為權限錯誤
      if (errorString.includes('Specified permissions are not sufficient') ||
          errorString.includes('SpreadsheetApp.getActiveSpreadsheet')) {
        console.log(`🚨 檢測到權限錯誤，直接拋出，不進行KEY輪換`);
        throw error; // 權限錯誤不應該進行KEY輪換
      }

      // 檢查是否為配額錯誤
      if (isQuotaError(error) && currentRetry < maxRetries - 1) {
        console.log(`🔄 檢測到配額錯誤，輪換到下一組API Key...`);

        // 輪換到下一組API Key
        rotateToNextApiKey();

        currentRetry++;
        console.log(`🔄 已輪換KEY，準備第 ${currentRetry + 1} 次重試...`);

        // 短暫延遲後重試
        Utilities.sleep(500);
        continue;

      } else {
        // 非配額錯誤或已達最大重試次數，直接拋出錯誤
        console.log(`🚨 非配額錯誤或已達最大重試次數，停止重試`);
        throw error;
      }
    }
  }
  
  // 所有KEY都用完了
  const errorMessage = `❌ 所有 API Key 配額已用完
  
🔧 技術詳情：
• 總共嘗試了 ${maxRetries} 組API Key
• 最後錯誤: ${lastError?.message || lastError || 'unknown'}
• 建議: 請稍後再試或聯繫管理員增加API配額

📞 請將此錯誤訊息回報給開發團隊`;

  console.error(`🚨 所有API Key配額已用完: ${lastError?.message || lastError}`);
  throw new Error(errorMessage);
}

/**
 * 📊 獲取總API Key數量
 * @returns {number} API Key總數
 */
function getTotalApiKeys() {
  try {
    const keyString = getConfigValue('geminiApiKey');
    const keys = parseMultipleApiKeys(keyString);
    const totalKeys = keys.length;
    
    console.log(`📊 當前配置了 ${totalKeys} 組API Key`);
    return totalKeys;
    
  } catch (error) {
    console.error('❌ 獲取API Key總數失敗:', error);
    return 1; // 預設至少有1組KEY
  }
}

/**
 * 🔍 判斷是否為配額錯誤 (429錯誤)
 * @param {Error|string} error - 錯誤物件或錯誤訊息
 * @returns {boolean} 是否為配額錯誤
 */
function isQuotaError(error) {
  const errorString = error?.message || error?.toString() || String(error);
  
  // 檢查常見的配額錯誤標識
  const quotaErrorPatterns = [
    '429',
    'quota',
    'rate limit',
    'too many requests',
    'exceeded',
    'Resource has been exhausted'
  ];
  
  const isQuota = quotaErrorPatterns.some(pattern => 
    errorString.toLowerCase().includes(pattern.toLowerCase())
  );
  
  if (isQuota) {
    console.log(`🔍 檢測到配額錯誤: ${errorString.substring(0, 100)}...`);
  }
  
  return isQuota;
}

// ===== 🔧 專用包裝器函數已移除 =====
// 原因：實際代碼中直接使用 callApiWithRetry 包裝現有函數，
// 不需要額外的專用包裝器函數，避免代碼重複和混亂。
//
// 使用方式：
// - callGemini() 已經內部使用 callApiWithRetry
// - textToSpeechWithGemini() 已經內部使用 callApiWithRetry
// - callGeminiAudioDialog() 已經內部使用 callApiWithRetry

// ===== 📋 使用說明 =====
/*
🔄 統一API Key輪換包裝器使用方法：

1️⃣ 基本用法：
   const result = callApiWithRetry(yourApiFunction, param1, param2);

2️⃣ 已整合的函數（自動使用包裝器）：
   const textResult = callGemini(prompt, mode);           // ✅ 已整合
   const audioResult = callGeminiAudioDialog(text, ctx);  // ✅ 已整合
   const ttsResult = textToSpeechWithGemini(text, config); // ✅ 已整合

3️⃣ 錯誤處理：
   - 自動檢測429配額錯誤
   - 每個KEY重試一次
   - 全部失敗返回統一錯誤訊息

4️⃣ 日誌追蹤：
   - 詳細的console.log輸出
   - 重試次數和KEY使用狀況追蹤
   - 錯誤原因分析

📊 配置要求：
- APIKEY工作表B4: 多組API Key (Ctrl+Enter分隔)
- APIKEY工作表A5/B5: 輪換索引管理
- APIKEY工作表A6/B6: 更新時間追蹤

🎯 重構完成狀態：
- ✅ callGemini() - 使用統一包裝器
- ✅ callGeminiAudioDialog() - 使用統一包裝器
- ✅ textToSpeechWithGemini() - 使用統一包裝器
- 🔄 圖片生成API - 待整合
- 🔄 影片生成API - 待整合
*/
