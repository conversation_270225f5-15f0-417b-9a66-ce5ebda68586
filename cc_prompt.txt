你好CC！我需要你幫我重構tribe-line-bot專案的API Key輪換機制。

## 🎯 任務目標
創建統一的API Key輪換包裝器，讓所有API調用（TTS、對話語音、文字、圖片、影片）都能使用同一套KEY輪換機制。

## 📋 第一階段任務：創建統一包裝器

請創建新檔案 `core_api_wrapper.gs`，包含以下功能：

1. **統一API調用包裝器函數**：
   - 函數名：`callApiWithRetry(apiFunction, ...args)`
   - 邏輯：每換一個KEY就重試一次，不是無限重試
   - 全部KEY失敗後返回「所有 API Key 配額已用完」錯誤

2. **輔助函數**：
   - `getTotalApiKeys()` - 獲取總KEY數量
   - `isQuotaError(error)` - 判斷是否為429配額錯誤

3. **檔案標頭**：
   - 包含檔案名稱註釋
   - 依賴關係說明
   - 最後更新日期

## 🔧 技術要求
- 使用現有的 `rotateToNextApiKey()` 函數
- 使用現有的 `parseMultipleApiKeys()` 和 `getCurrentGeminiApiKey()` 函數
- 保持向後兼容性
- 添加詳細的console.log用於調試

請開始創建這個檔案，完成後告訴我結果。
