/*
 * 檔案: core_module_manager.gs
 * 分類: core
 * 功能開關: -
 * 描述: 模組自動管理系統，根據試算表開關自動移動檔案，實現熱插拔功能模組
 * 依賴: [core_feature_toggle.gs, core_config.gs]
 * 最後更新: 2025-01-14 - 添加API_WRAPPER模組映射
 */

// core_module_manager.gs
// == 模組自動管理系統 ==
// 🔄 根據試算表開關自動移動檔案，實現熱插拔功能模組

/**
 * 🎛️ 功能模組檔案映射表
 * 定義每個功能對應的檔案前綴和檔案清單
 */
const MODULE_FILE_MAPPING = {
  IMAGE_GENERATION: {
    prefix: 'modules_image',
    files: [
      'modules_image_core.gs',
      'modules_image_push.gs',
      'modules_image_buttons.gs',
      'modules_image_utils.gs',
      'modules_image_generator.gs'
    ],
    description: '圖片生成功能模組'
  },

  TEXT_TO_SPEECH: {
    prefix: 'modules_audio',
    files: [
      'modules_audio_handler.gs',
      'modules_audio_advanced.gs'
    ],
    description: '音頻功能模組'
  },

  CONVERSATIONAL_AUDIO: {
    prefix: 'modules_audio',
    files: [
      'modules_audio_handler.gs',
      'modules_audio_advanced.gs'
    ],
    description: '對話音頻功能模組'
  },

  NOTE_TAKING: {
    prefix: 'modules_note',
    files: [
      'modules_note_memory.gs'
    ],
    description: '筆記管理功能模組'
  },

  FILE_QUERY: {
    prefix: 'modules_file',
    files: [
      'modules_file_extractor.gs',
      'modules_file_drive.gs'
    ],
    description: '檔案查詢功能模組'
  },

  CONVERSATION_REVIEW: {
    prefix: 'modules_note',
    files: [
      'modules_note_memory.gs'
    ],
    description: '對話回顧功能模組'
  },

  GROUP_MEMBER_QUERY: {
    prefix: 'modules_group',
    files: [
      'modules_group_tracker.gs'
    ],
    description: '群組成員查詢功能模組'
  },

  GROUP_CHAT_TRACKING: {
    prefix: 'modules_group',
    files: [
      'modules_group_tracker.gs'
    ],
    description: '群組聊天追蹤功能模組'
  },

  SOCIAL_MEDIA_POST: {
    prefix: 'modules_social',
    files: [
      // 功能實現在 AI 處理器中，暫時沒有專門檔案
    ],
    description: '社交媒體發文功能模組'
  },

  KNOWLEDGE_BASE: {
    prefix: 'modules_knowledge',
    files: [
      'modules_knowledge_base.gs'
    ],
    description: '知識庫功能模組'
  },

  API_WRAPPER: {
    prefix: 'core_api',
    files: [
      'core_api_wrapper.gs'
    ],
    description: '統一API Key輪換包裝器模組'
  }
};

/**
 * 🔄 自動模組管理主函數
 * 根據試算表設定自動啟用/停用功能模組
 */
function autoManageModules() {
  try {
    console.log('🔄 開始自動模組管理...');
    
    const results = {};
    
    // 遍歷所有功能模組
    for (const [featureName, moduleInfo] of Object.entries(MODULE_FILE_MAPPING)) {
      console.log(`🔍 檢查功能: ${featureName} (${moduleInfo.description})`);
      
      // 檢查試算表中的開關狀態
      const isEnabled = isFeatureEnabled(featureName);
      console.log(`📊 ${featureName} 開關狀態: ${isEnabled ? '啟用' : '停用'}`);
      
      if (isEnabled) {
        // 功能啟用：確保檔案在根目錄
        results[featureName] = activateModule(featureName, moduleInfo);
      } else {
        // 功能停用：將檔案移到 _sleeping
        results[featureName] = deactivateModule(featureName, moduleInfo);
      }
    }
    
    console.log('✅ 自動模組管理完成');
    return results;
    
  } catch (error) {
    console.error('❌ 自動模組管理失敗:', error);
    return { error: error.message };
  }
}

/**
 * ✅ 啟用模組：將檔案從 _sleeping 移動到根目錄
 */
function activateModule(featureName, moduleInfo) {
  const results = {
    feature: featureName,
    action: 'activate',
    files: {},
    success: true,
    errors: []
  };

  try {
    console.log(`✅ 啟用模組: ${featureName}`);
    
    // 安全檢查：確保 moduleInfo.files 存在且是陣列
    if (!moduleInfo.files || !Array.isArray(moduleInfo.files)) {
      console.warn(`⚠️ 功能 ${featureName} 的檔案清單無效或缺失`);
      results.success = false;
      results.errors.push(`功能 ${featureName} 的檔案清單無效或缺失`);
      return results;
    }
    
    for (const fileName of moduleInfo.files) {
      try {
        const moveResult = moveFileToRoot(fileName);
        results.files[fileName] = moveResult;
        
        if (!moveResult.success) {
          results.success = false;
          results.errors.push(`${fileName}: ${moveResult.message}`);
        }
        
      } catch (fileError) {
        console.error(`❌ 處理檔案 ${fileName} 時發生錯誤:`, fileError);
        results.files[fileName] = { success: false, message: fileError.message };
        results.success = false;
        results.errors.push(`${fileName}: ${fileError.message}`);
      }
    }
    
  } catch (error) {
    console.error(`❌ 啟用模組 ${featureName} 失敗:`, error);
    results.success = false;
    results.errors.push(error.message);
  }

  return results;
}

/**
 * ❌ 停用模組：將檔案從根目錄移動到 _sleeping
 */
function deactivateModule(featureName, moduleInfo) {
  const results = {
    feature: featureName,
    action: 'deactivate',
    files: {},
    success: true,
    errors: []
  };

  try {
    console.log(`❌ 停用模組: ${featureName}`);
    
    // 安全檢查：確保 moduleInfo.files 存在且是陣列
    if (!moduleInfo.files || !Array.isArray(moduleInfo.files)) {
      console.warn(`⚠️ 功能 ${featureName} 的檔案清單無效或缺失`);
      results.success = false;
      results.errors.push(`功能 ${featureName} 的檔案清單無效或缺失`);
      return results;
    }
    
    for (const fileName of moduleInfo.files) {
      try {
        const moveResult = moveFileToSleeping(fileName);
        results.files[fileName] = moveResult;
        
        if (!moveResult.success) {
          results.success = false;
          results.errors.push(`${fileName}: ${moveResult.message}`);
        }
        
      } catch (fileError) {
        console.error(`❌ 處理檔案 ${fileName} 時發生錯誤:`, fileError);
        results.files[fileName] = { success: false, message: fileError.message };
        results.success = false;
        results.errors.push(`${fileName}: ${fileError.message}`);
      }
    }
    
  } catch (error) {
    console.error(`❌ 停用模組 ${featureName} 失敗:`, error);
    results.success = false;
    results.errors.push(error.message);
  }

  return results;
}

/**
 * 📁 將檔案移動到根目錄
 */
function moveFileToRoot(fileName) {
  try {
    // 檢查檔案是否在 _sleeping 資料夾
    const inSleeping = checkFileExists(fileName, '_sleeping');
    const inRoot = checkFileExists(fileName, 'root');
    
    if (inRoot && !inSleeping) {
      return { success: true, message: '檔案已在根目錄', action: 'none' };
    }
    
    if (!inSleeping) {
      return { success: false, message: '檔案不存在於 _sleeping 資料夾', action: 'none' };
    }
    
    // 模擬檔案移動（實際上 Google Apps Script 無法真正移動檔案）
    console.log(`📁 模擬移動檔案: ${fileName} 從 _sleeping 到根目錄`);
    
    // 使用 PropertiesService 記錄檔案狀態
    const properties = PropertiesService.getScriptProperties();
    properties.setProperty(`file_location_${fileName}`, 'root');
    
    return { 
      success: true, 
      message: '檔案已移動到根目錄', 
      action: 'moved',
      note: '使用 PropertiesService 模擬檔案移動'
    };
    
  } catch (error) {
    console.error(`❌ 移動檔案到根目錄失敗: ${fileName}`, error);
    return { success: false, message: error.message, action: 'error' };
  }
}

/**
 * 📁 將檔案移動到 _sleeping 資料夾
 */
function moveFileToSleeping(fileName) {
  try {
    // 檢查檔案是否在根目錄
    const inRoot = checkFileExists(fileName, 'root');
    const inSleeping = checkFileExists(fileName, '_sleeping');
    
    if (inSleeping && !inRoot) {
      return { success: true, message: '檔案已在 _sleeping 資料夾', action: 'none' };
    }
    
    if (!inRoot) {
      return { success: false, message: '檔案不存在於根目錄', action: 'none' };
    }
    
    // 模擬檔案移動
    console.log(`📁 模擬移動檔案: ${fileName} 從根目錄到 _sleeping`);
    
    // 使用 PropertiesService 記錄檔案狀態
    const properties = PropertiesService.getScriptProperties();
    properties.setProperty(`file_location_${fileName}`, '_sleeping');
    
    return { 
      success: true, 
      message: '檔案已移動到 _sleeping 資料夾', 
      action: 'moved',
      note: '使用 PropertiesService 模擬檔案移動'
    };
    
  } catch (error) {
    console.error(`❌ 移動檔案到 _sleeping 失敗: ${fileName}`, error);
    return { success: false, message: error.message, action: 'error' };
  }
}

/**
 * 🔍 檢查檔案是否存在於指定位置
 */
function checkFileExists(fileName, location) {
  try {
    if (location === 'root') {
      // 檢查檔案是否在根目錄（實際檢查或使用 PropertiesService）
      const properties = PropertiesService.getScriptProperties();
      const storedLocation = properties.getProperty(`file_location_${fileName}`);

      // 如果沒有記錄，假設檔案在根目錄
      return storedLocation === null || storedLocation === 'root';

    } else if (location === '_sleeping') {
      // 檢查檔案是否在 _sleeping 資料夾
      const properties = PropertiesService.getScriptProperties();
      const storedLocation = properties.getProperty(`file_location_${fileName}`);

      return storedLocation === '_sleeping';
    }

    return false;

  } catch (error) {
    console.error(`❌ 檢查檔案存在性失敗: ${fileName}`, error);
    return false;
  }
}

/**
 * 📊 生成模組狀態報告
 */
function generateModuleReport() {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: 0,
      active: 0,
      sleeping: 0,
      missing: 0
    },
    modules: {}
  };

  for (const [featureName, moduleInfo] of Object.entries(MODULE_FILE_MAPPING)) {
    const isEnabled = isFeatureEnabled(featureName, false); // 不觸發自動管理
    const moduleStatus = {
      enabled: isEnabled,
      description: moduleInfo.description,
      files: {}
    };

    // 安全檢查：確保 moduleInfo.files 存在且是陣列
    if (!moduleInfo.files || !Array.isArray(moduleInfo.files)) {
      console.warn(`⚠️ 功能 ${featureName} 的檔案清單無效或缺失`);
      moduleStatus.files = {};
      report.modules[featureName] = moduleStatus;
      continue;
    }

    for (const fileName of moduleInfo.files) {
      const inRoot = checkFileExists(fileName, 'root');
      const inSleeping = checkFileExists(fileName, '_sleeping');

      moduleStatus.files[fileName] = {
        inRoot: inRoot,
        inSleeping: inSleeping,
        status: inRoot && !inSleeping ? 'active' : inSleeping ? 'sleeping' : 'missing'
      };

      // 統計
      if (inRoot && !inSleeping) report.summary.active++;
      else if (inSleeping) report.summary.sleeping++;
      else report.summary.missing++;
    }

    report.modules[featureName] = moduleStatus;
    // 安全地計算總檔案數
    report.summary.total += moduleInfo.files.length;
  }

  console.log('📊 模組狀態報告:', JSON.stringify(report, null, 2));
  return report;
}

/**
 * 🔄 重置所有檔案狀態
 * 將所有檔案狀態重置為根目錄（清除 PropertiesService 記錄）
 */
function resetAllFileStates() {
  try {
    console.log('🔄 重置所有檔案狀態...');

    const properties = PropertiesService.getScriptProperties();
    const allFiles = [];

    // 收集所有檔案名稱
    for (const [featureName, moduleInfo] of Object.entries(MODULE_FILE_MAPPING)) {
      // 安全檢查：確保 moduleInfo.files 存在且是陣列
      if (moduleInfo.files && Array.isArray(moduleInfo.files)) {
        allFiles.push(...moduleInfo.files);
      }
    }

    // 移除重複檔案
    const uniqueFiles = [...new Set(allFiles)];

    // 清除所有檔案位置記錄
    for (const fileName of uniqueFiles) {
      properties.deleteProperty(`file_location_${fileName}`);
      console.log(`🗑️ 已清除檔案狀態記錄: ${fileName}`);
    }

    console.log(`✅ 已重置 ${uniqueFiles.length} 個檔案的狀態記錄`);
    return { success: true, resetCount: uniqueFiles.length };

  } catch (error) {
    console.error('❌ 重置檔案狀態失敗:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 🧪 測試模組管理系統
 */
function testModuleManager() {
  console.log('🧪 測試模組管理系統...');

  try {
    // 1. 生成初始狀態報告
    console.log('📊 步驟1: 生成初始狀態報告');
    const initialReport = generateModuleReport();

    // 2. 測試自動管理
    console.log('🔄 步驟2: 測試自動模組管理');
    const manageResult = autoManageModules();

    // 3. 生成管理後狀態報告
    console.log('📊 步驟3: 生成管理後狀態報告');
    const finalReport = generateModuleReport();

    return {
      initialReport: initialReport,
      manageResult: manageResult,
      finalReport: finalReport,
      success: true
    };

  } catch (error) {
    console.error('❌ 測試模組管理系統失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// ===== 🛡️ 自動驗證和發現機制（安全網） =====

/**
 * 🔍 自動驗證映射表的完整性和正確性
 * 檢查映射表中的檔案是否存在、是否有重複、前綴是否一致等
 */
function validateModuleMapping() {
  console.log('🔍 開始驗證模組映射表...');

  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalFeatures: 0,
      totalFiles: 0,
      validFeatures: 0,
      invalidFeatures: 0,
      missingFiles: 0,
      duplicateFiles: 0,
      prefixMismatches: 0
    },
    issues: {
      missingFiles: [],
      duplicateFiles: [],
      prefixMismatches: [],
      orphanedFeatures: [],
      missingMappings: []
    },
    suggestions: []
  };

  // 收集所有檔案和檢查重複
  const allFiles = [];
  const fileFeatureMap = {};

  // 1. 驗證映射表中的每個功能
  for (const [featureName, moduleInfo] of Object.entries(MODULE_FILE_MAPPING)) {
    report.summary.totalFeatures++;
    let featureValid = true;

    console.log(`🔍 檢查功能: ${featureName}`);

    // 檢查功能是否在 FEATURE_TOGGLES 中存在
    if (typeof FEATURE_TOGGLES !== 'undefined' && !FEATURE_TOGGLES.hasOwnProperty(featureName)) {
      report.issues.orphanedFeatures.push({
        feature: featureName,
        issue: '功能在映射表中存在但在 FEATURE_TOGGLES 中不存在',
        suggestion: `在 core_feature_toggle.gs 的 FEATURE_TOGGLES 中添加 ${featureName}`
      });
      featureValid = false;
    }

    // 檢查每個檔案 - 增加安全檢查
    if (moduleInfo.files && Array.isArray(moduleInfo.files)) {
      for (const fileName of moduleInfo.files) {
        report.summary.totalFiles++;
        allFiles.push(fileName);

        // 記錄檔案屬於哪些功能（檢查重複）
        if (fileFeatureMap[fileName]) {
          fileFeatureMap[fileName].push(featureName);
        } else {
          fileFeatureMap[fileName] = [featureName];
        }

        // 檢查檔案是否存在
        const fileExists = checkFileExistsByFunction(fileName);
        if (!fileExists) {
          report.issues.missingFiles.push({
            feature: featureName,
            file: fileName,
            suggestion: `創建檔案 ${fileName} 或從映射表中移除`
          });
          report.summary.missingFiles++;
          featureValid = false;
        }

        // 檢查檔案名稱是否符合前綴規範
        if (moduleInfo.prefix && !fileName.startsWith(moduleInfo.prefix)) {
          report.issues.prefixMismatches.push({
            feature: featureName,
            file: fileName,
            expectedPrefix: moduleInfo.prefix,
            suggestion: `重命名檔案為 ${moduleInfo.prefix}_xxx.gs 或更新前綴設定`
          });
          report.summary.prefixMismatches++;
          featureValid = false;
        }
      }
    } else {
      // 如果 files 不存在或不是陣列，記錄為問題
      report.issues.missingFiles.push({
        feature: featureName,
        file: 'N/A',
        suggestion: `功能 ${featureName} 的檔案清單無效或缺失`
      });
      report.summary.missingFiles++;
      featureValid = false;
    }

    if (featureValid) {
      report.summary.validFeatures++;
    } else {
      report.summary.invalidFeatures++;
    }
  }

  // 2. 檢查重複檔案
  for (const [fileName, features] of Object.entries(fileFeatureMap)) {
    // 安全檢查：確保 features 存在且是陣列
    if (!features || !Array.isArray(features)) {
      console.warn(`⚠️ 檔案 ${fileName} 的功能清單無效:`, features);
      continue;
    }
    
    if (features.length > 1) {
      report.issues.duplicateFiles.push({
        file: fileName,
        features: features,
        suggestion: `檔案 ${fileName} 被多個功能使用，確認這是否正確`
      });
      report.summary.duplicateFiles++;
    }
  }

  // 3. 生成建議
  if (report.summary.missingFiles > 0) {
    report.suggestions.push('🔧 建議執行 discoverModuleFiles() 來發現可能遺漏的檔案');
  }

  if (report.summary.orphanedFeatures.length > 0) {
    report.suggestions.push('🔧 建議檢查 core_feature_toggle.gs 中的 FEATURE_TOGGLES 配置');
  }

  if (report.summary.prefixMismatches > 0) {
    report.suggestions.push('🔧 建議統一檔案命名規範或更新前綴設定');
  }

  console.log('✅ 映射表驗證完成');
  console.log(`📊 總計: ${report.summary.totalFeatures} 功能, ${report.summary.totalFiles} 檔案`);
  console.log(`❌ 問題: ${report.summary.missingFiles} 缺失檔案, ${report.summary.duplicateFiles} 重複檔案`);

  return report;
}

/**
 * 🔍 檢查檔案是否存在（基於函數名稱推測）
 * 這是一個改進版的檔案存在性檢查
 */
function checkFileExistsByFunction(fileName) {
  try {
    // 檢查輸入參數
    if (!fileName || typeof fileName !== 'string') {
      console.warn(`⚠️ 無效的檔案名稱: ${fileName}`);
      return false;
    }

    // 擴展的函數映射表，基於實際觀察到的檔案和函數
    const functionMap = {
      // 圖片模組
      'modules_image_core.gs': 'handleAIImageGeneration_PassiveChain',
      'modules_image_push.gs': 'pushImageResultV143',
      'modules_image_buttons.gs': 'handleStoryParamsButton',
      'modules_image_utils.gs': 'shortenUrl',
      'modules_image_generator.gs': 'executeAsyncImageGenerationV143',

      // 音頻模組
      'modules_audio_handler.gs': 'handleAITTSRequest',
      'modules_audio_advanced.gs': 'textToSpeechWithGemini',

      // 其他模組
      'modules_ai_features.gs': 'callGeminiWithSmartSearch',
      'modules_line_processor_core.gs': 'handleTextMessageAIFirst',
      'modules_line_webhook.gs': 'doPost',
      'modules_group_tracker.gs': 'recordGroupMessage',
      'modules_note_memory.gs': 'handleAINoteRequest',
      'modules_file_extractor.gs': 'extractTextForTTS',
      'modules_file_drive.gs': 'handleGoogleDriveLink',
      'modules_knowledge_base.gs': 'searchKnowledgeBase'
    };

    const expectedFunction = functionMap[fileName];
    if (expectedFunction) {
      try {
        const func = eval(expectedFunction);
        return typeof func === 'function';
      } catch (error) {
        return false;
      }
    }

    // 如果沒有預定義的函數映射，嘗試基於檔案名稱推測
    const baseName = fileName.replace('.gs', '').replace('modules_', '');

    // 檢查 baseName 是否有效
    if (!baseName || typeof baseName !== 'string' || baseName.length === 0) {
      console.warn(`⚠️ 無法從檔案名稱 ${fileName} 推測函數名稱`);
      return false;
    }

    // 安全地處理字串操作
    let capitalizedBaseName;
    try {
      capitalizedBaseName = baseName.charAt(0).toUpperCase() + baseName.slice(1);
    } catch (error) {
      console.warn(`⚠️ 處理檔案名稱 ${fileName} 時發生錯誤:`, error);
      return false;
    }

    const possibleFunctions = [
      `handle${capitalizedBaseName}`,
      `${baseName}Handler`,
      `process${capitalizedBaseName}`
    ];

    for (const funcName of possibleFunctions) {
      try {
        const func = eval(funcName);
        if (typeof func === 'function') {
          return true;
        }
      } catch (error) {
        // 繼續嘗試下一個函數名稱
      }
    }

    return false;
  } catch (error) {
    console.error(`❌ 檢查檔案 ${fileName} 時發生錯誤:`, error);
    return false;
  }
}

/**
 * 🔍 自動發現項目中的模組檔案
 * 掃描並分析可能遺漏的 modules_ 檔案
 */
function discoverModuleFiles() {
  console.log('🔍 開始自動發現模組檔案...');

  const discovery = {
    timestamp: new Date().toISOString(),
    discoveredFiles: [],
    suggestedMappings: {},
    unmappedFiles: [],
    summary: {
      totalDiscovered: 0,
      alreadyMapped: 0,
      needsMapping: 0
    }
  };

  // 已知的模組檔案列表（基於實際觀察）
  const knownModuleFiles = [
    'modules_ai_features.gs',
    'modules_audio_handler.gs',
    'modules_audio_advanced.gs',
    'modules_image_core.gs',
    'modules_image_push.gs',
    'modules_image_buttons.gs',
    'modules_image_utils.gs',
    'modules_image_generator.gs',
    'modules_line_processor_core.gs',
    'modules_line_webhook.gs',
    'modules_line_media.gs',
    'modules_group_tracker.gs',
    'modules_note_memory.gs',
    'modules_file_handler.gs',
    'modules_conversation_review.gs',
    'modules_social_media.gs',
    'modules_knowledge_base.gs'
  ];

  // 收集映射表中已有的檔案
  const mappedFiles = new Set();
  for (const moduleInfo of Object.values(MODULE_FILE_MAPPING)) {
    // 安全檢查：確保 moduleInfo.files 存在且是陣列
    if (moduleInfo.files && Array.isArray(moduleInfo.files)) {
      for (const file of moduleInfo.files) {
        mappedFiles.add(file);
      }
    }
  }

  // 檢查每個已知檔案
  for (const fileName of knownModuleFiles) {
    const exists = checkFileExistsByFunction(fileName);
    if (exists) {
      discovery.discoveredFiles.push(fileName);
      discovery.summary.totalDiscovered++;

      if (mappedFiles.has(fileName)) {
        discovery.summary.alreadyMapped++;
      } else {
        discovery.unmappedFiles.push(fileName);
        discovery.summary.needsMapping++;

        // 基於檔案名稱建議映射
        const suggestedMapping = suggestMappingForFile(fileName);
        if (suggestedMapping) {
          discovery.suggestedMappings[fileName] = suggestedMapping;
        }
      }
    }
  }

  console.log('✅ 檔案發現完成');
  console.log(`📊 發現 ${discovery.summary.totalDiscovered} 個檔案`);
  console.log(`📋 已映射: ${discovery.summary.alreadyMapped}, 需要映射: ${discovery.summary.needsMapping}`);

  return discovery;
}

/**
 * 🎯 為檔案建議映射關係
 */
function suggestMappingForFile(fileName) {
  const suggestions = {
    'modules_ai_features.gs': {
      features: ['SMART_SEARCH', 'EXPERIMENTAL_FEATURES'],
      prefix: 'modules_ai',
      description: 'AI 智能功能模組'
    },
    'modules_line_processor_core.gs': {
      features: ['SYSTEM_HELP'],
      prefix: 'modules_line',
      description: 'LINE 核心處理模組'
    },
    'modules_line_webhook.gs': {
      features: ['SYSTEM_HELP'],
      prefix: 'modules_line',
      description: 'LINE Webhook 處理模組'
    },
    'modules_line_media.gs': {
      features: ['DRIVE_LINK_SHARING'],
      prefix: 'modules_line',
      description: 'LINE 媒體處理模組'
    }
  };

  return suggestions[fileName] || null;
}

/**
 * 🛡️ 綜合安全網檢查
 * 執行完整的驗證和發現流程，提供統一的安全網報告
 */
function runSafetyNetCheck() {
  console.log('🛡️ 開始執行綜合安全網檢查...');

  const safetyReport = {
    timestamp: new Date().toISOString(),
    validation: null,
    discovery: null,
    recommendations: [],
    criticalIssues: [],
    overallHealth: 'unknown'
  };

  try {
    // 1. 執行映射表驗證
    console.log('📋 步驟1: 驗證映射表');
    safetyReport.validation = validateModuleMapping();

    // 2. 執行檔案發現
    console.log('🔍 步驟2: 發現模組檔案');
    safetyReport.discovery = discoverModuleFiles();

    // 3. 分析結果並生成建議
    console.log('🎯 步驟3: 分析結果');
    analyzeAndGenerateRecommendations(safetyReport);

    // 4. 評估整體健康狀況
    safetyReport.overallHealth = assessOverallHealth(safetyReport);

    console.log('✅ 安全網檢查完成');
    console.log(`🏥 整體健康狀況: ${safetyReport.overallHealth}`);

    return safetyReport;

  } catch (error) {
    console.error('❌ 安全網檢查失敗:', error);
    safetyReport.criticalIssues.push({
      type: 'system_error',
      message: `安全網檢查過程中發生錯誤: ${error.message}`,
      severity: 'critical'
    });
    safetyReport.overallHealth = 'critical';
    return safetyReport;
  }
}

/**
 * 🎯 分析檢查結果並生成建議
 */
function analyzeAndGenerateRecommendations(safetyReport) {
  const validation = safetyReport.validation;
  const discovery = safetyReport.discovery;

  // 分析驗證結果
  if (validation.summary.missingFiles > 0) {
    safetyReport.criticalIssues.push({
      type: 'missing_files',
      count: validation.summary.missingFiles,
      message: `映射表中有 ${validation.summary.missingFiles} 個檔案不存在`,
      severity: 'high'
    });
    safetyReport.recommendations.push({
      priority: 'high',
      action: 'fix_missing_files',
      description: '修復缺失的檔案或更新映射表',
      details: validation.issues.missingFiles
    });
  }

  if (validation.issues.orphanedFeatures.length > 0) {
    safetyReport.criticalIssues.push({
      type: 'orphaned_features',
      count: validation.issues.orphanedFeatures.length,
      message: `有 ${validation.issues.orphanedFeatures.length} 個功能在映射表中但不在功能開關中`,
      severity: 'medium'
    });
    safetyReport.recommendations.push({
      priority: 'medium',
      action: 'sync_feature_toggles',
      description: '同步功能開關配置',
      details: validation.issues.orphanedFeatures
    });
  }

  // 分析發現結果
  if (discovery.summary.needsMapping > 0) {
    safetyReport.recommendations.push({
      priority: 'medium',
      action: 'add_missing_mappings',
      description: `發現 ${discovery.summary.needsMapping} 個未映射的檔案`,
      details: discovery.unmappedFiles,
      suggestions: discovery.suggestedMappings
    });
  }

  // 生成自動修復建議
  if (Object.keys(discovery.suggestedMappings).length > 0) {
    safetyReport.recommendations.push({
      priority: 'low',
      action: 'auto_fix_available',
      description: '可以自動生成部分映射關係',
      details: 'executeAutoFix() 函數可以自動添加建議的映射'
    });
  }
}

/**
 * 🏥 評估整體健康狀況
 */
function assessOverallHealth(safetyReport) {
  const validation = safetyReport.validation;
  const criticalCount = safetyReport.criticalIssues.filter(i => i.severity === 'critical').length;
  const highCount = safetyReport.criticalIssues.filter(i => i.severity === 'high').length;
  const mediumCount = safetyReport.criticalIssues.filter(i => i.severity === 'medium').length;

  if (criticalCount > 0) {
    return 'critical';
  } else if (highCount > 0) {
    return 'poor';
  } else if (mediumCount > 2) {
    return 'fair';
  } else if (validation.summary.validFeatures / validation.summary.totalFeatures >= 0.8) {
    return 'good';
  } else {
    return 'fair';
  }
}

/**
 * 🔧 自動修復建議的映射關係
 * 根據發現的檔案自動添加映射（需要確認）
 */
function executeAutoFix(confirmFix = false) {
  if (!confirmFix) {
    console.log('⚠️ 自動修復需要確認，請設定 confirmFix = true');
    return {
      success: false,
      message: '需要確認才能執行自動修復',
      previewChanges: generateAutoFixPreview()
    };
  }

  console.log('🔧 開始執行自動修復...');

  const discovery = discoverModuleFiles();
  const fixes = [];

  for (const [fileName, suggestion] of Object.entries(discovery.suggestedMappings)) {
    // 這裡只是示例，實際修復需要更新 MODULE_FILE_MAPPING
    console.log(`🔧 建議為 ${fileName} 添加映射到功能: ${suggestion.features.join(', ')}`);
    fixes.push({
      file: fileName,
      action: 'add_mapping',
      suggestion: suggestion
    });
  }

  return {
    success: true,
    message: `已分析 ${fixes.length} 個修復建議`,
    fixes: fixes,
    note: '實際修復需要手動更新 MODULE_FILE_MAPPING 常數'
  };
}

/**
 * 📋 生成自動修復預覽
 */
function generateAutoFixPreview() {
  const discovery = discoverModuleFiles();
  const preview = [];

  for (const [fileName, suggestion] of Object.entries(discovery.suggestedMappings)) {
    preview.push({
      action: `添加 ${fileName} 到功能 ${suggestion.features.join(', ')}`,
      code: `// 在 MODULE_FILE_MAPPING 中添加:\n${suggestion.features[0]}: {\n  prefix: '${suggestion.prefix}',\n  files: ['${fileName}'],\n  description: '${suggestion.description}'\n}`
    });
  }

  return preview;
}

/**
 * 🧪 測試安全網系統
 */
function testSafetyNet() {
  console.log('🧪 測試安全網系統...');

  try {
    // 1. 測試驗證功能
    console.log('📋 測試映射表驗證...');
    const validation = validateModuleMapping();

    // 2. 測試發現功能
    console.log('🔍 測試檔案發現...');
    const discovery = discoverModuleFiles();

    // 3. 測試綜合檢查
    console.log('🛡️ 測試綜合安全網...');
    const safetyReport = runSafetyNetCheck();

    // 4. 測試自動修復預覽
    console.log('🔧 測試自動修復預覽...');
    const autoFixPreview = executeAutoFix(false);

    const testResults = {
      validation: {
        success: validation.summary.totalFeatures > 0,
        features: validation.summary.totalFeatures,
        issues: validation.summary.missingFiles + validation.summary.duplicateFiles
      },
      discovery: {
        success: discovery.summary.totalDiscovered > 0,
        discovered: discovery.summary.totalDiscovered,
        unmapped: discovery.summary.needsMapping
      },
      safetyNet: {
        success: safetyReport.overallHealth !== 'critical',
        health: safetyReport.overallHealth,
        recommendations: safetyReport.recommendations.length
      },
      autoFix: {
        success: autoFixPreview.previewChanges && autoFixPreview.previewChanges.length >= 0,
        suggestions: autoFixPreview.previewChanges ? autoFixPreview.previewChanges.length : 0
      }
    };

    console.log('✅ 安全網系統測試完成');
    console.log(`📊 驗證: ${testResults.validation.features} 功能, ${testResults.validation.issues} 問題`);
    console.log(`🔍 發現: ${testResults.discovery.discovered} 檔案, ${testResults.discovery.unmapped} 未映射`);
    console.log(`🏥 健康: ${testResults.safetyNet.health}, ${testResults.safetyNet.recommendations} 建議`);

    return testResults;

  } catch (error) {
    console.error('❌ 安全網系統測試失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// ===== 📋 使用說明 =====
/*
🔄 模組自動管理系統使用方法：

1️⃣ 自動管理（推薦）：
   autoManageModules(); // 根據功能開關自動管理所有模組

2️⃣ 手動管理：
   activateModule('IMAGE_GENERATION', MODULE_FILE_MAPPING['IMAGE_GENERATION']);
   deactivateModule('TEXT_TO_SPEECH', MODULE_FILE_MAPPING['TEXT_TO_SPEECH']);

3️⃣ 狀態檢查：
   generateModuleReport(); // 查看所有模組狀態
   checkFileExists('modules_image_core.gs', 'root'); // 檢查特定檔案

4️⃣ 系統維護：
   resetAllFileStates(); // 重置所有檔案狀態
   testModuleManager();  // 測試系統功能

5️⃣ 🛡️ 安全網功能（新增）：
   runSafetyNetCheck();     // 執行綜合安全網檢查
   validateModuleMapping(); // 驗證映射表完整性
   discoverModuleFiles();   // 發現未映射的檔案
   executeAutoFix(true);    // 執行自動修復（需確認）
   testSafetyNet();         // 測試安全網系統

6️⃣ 檔案映射：
   - 在 MODULE_FILE_MAPPING 中定義功能與檔案的對應關係
   - 支援多個檔案對應一個功能
   - 支援檔案前綴分類

⚠️ 重要說明：
- 由於 Google Apps Script 限制，實際檔案移動使用 PropertiesService 模擬
- 檔案狀態記錄在腳本屬性中，重新部署後可能需要重置
- 建議定期使用 runSafetyNetCheck() 檢查系統健康狀況

🛡️ 安全網說明：
- 自動驗證映射表的完整性和正確性
- 發現可能遺漏的模組檔案
- 提供自動修復建議和預覽
- 評估整體系統健康狀況
- 防止因映射錯誤導致的功能失效

🔗 整合說明：
- 與 core_feature_toggle.gs 緊密整合
- 支援自動同步功能開關狀態
- 提供完整的模組生命週期管理
- 內建安全網機制確保系統穩定性

🔧 修復說明（2025-07-05）：
- 修復了所有可能導致 "Cannot read properties of undefined (reading 'length')" 的錯誤
- 在所有訪問 moduleInfo.files 的地方增加了安全檢查
- 確保代碼在 moduleInfo.files 為 undefined 或 null 時不會崩潰
- 提供更好的錯誤處理和警告訊息
*/
